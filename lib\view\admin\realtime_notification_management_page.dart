import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/realtime_notification_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/realtime_notification_model.dart';

class RealtimeNotificationManagementPage extends StatefulWidget {
  const RealtimeNotificationManagementPage({Key? key}) : super(key: key);

  @override
  State<RealtimeNotificationManagementPage> createState() =>
      _RealtimeNotificationManagementPageState();
}

class _RealtimeNotificationManagementPageState
    extends State<RealtimeNotificationManagementPage> {
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _targetScreenController = TextEditingController();

  String _selectedType = 'system';
  String _selectedPriority = 'normal';
  bool _isPublic = true;
  int _expiresInDays = 30;

  @override
  void dispose() {
    _titleController.dispose();
    _bodyController.dispose();
    _imageUrlController.dispose();
    _targetScreenController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RealtimeNotificationController>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Quản lý thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              controller.refreshNotifications();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Form tạo thông báo mới
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tạo thông báo mới',
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Tiêu đề
                  TextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: 'Tiêu đề',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Nội dung
                  TextField(
                    controller: _bodyController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: 'Nội dung',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // URL hình ảnh
                  TextField(
                    controller: _imageUrlController,
                    decoration: InputDecoration(
                      labelText: 'URL hình ảnh (tùy chọn)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Target screen
                  TextField(
                    controller: _targetScreenController,
                    decoration: InputDecoration(
                      labelText: 'Màn hình đích (tùy chọn)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      hintText: 'Ví dụ: movie_detail, ticket, promo',
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Các tùy chọn
                  Row(
                    children: [
                      // Loại thông báo
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedType,
                          decoration: InputDecoration(
                            labelText: 'Loại',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(
                                value: 'system', child: Text('Hệ thống')),
                            DropdownMenuItem(
                                value: 'movie', child: Text('Phim')),
                            DropdownMenuItem(
                                value: 'promo', child: Text('Khuyến mãi')),
                            DropdownMenuItem(
                                value: 'ticket', child: Text('Vé')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                            });
                          },
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Độ ưu tiên
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPriority,
                          decoration: InputDecoration(
                            labelText: 'Độ ưu tiên',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'low', child: Text('Thấp')),
                            DropdownMenuItem(
                                value: 'normal', child: Text('Bình thường')),
                            DropdownMenuItem(value: 'high', child: Text('Cao')),
                            DropdownMenuItem(
                                value: 'urgent', child: Text('Khẩn cấp')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedPriority = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Công khai và thời hạn
                  Row(
                    children: [
                      Expanded(
                        child: CheckboxListTile(
                          title: const Text('Thông báo công khai'),
                          value: _isPublic,
                          onChanged: (value) {
                            setState(() {
                              _isPublic = value!;
                            });
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                      ),
                      Expanded(
                        child: DropdownButtonFormField<int>(
                          value: _expiresInDays,
                          decoration: InputDecoration(
                            labelText: 'Hết hạn sau (ngày)',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(value: 1, child: Text('1 ngày')),
                            DropdownMenuItem(value: 7, child: Text('7 ngày')),
                            DropdownMenuItem(value: 30, child: Text('30 ngày')),
                            DropdownMenuItem(value: 90, child: Text('90 ngày')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _expiresInDays = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Nút tạo
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _createNotification(controller),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xff4B79A1),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Tạo thông báo',
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Danh sách thông báo
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Danh sách thông báo',
                      style: GoogleFonts.mulish(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: Obx(() {
                        if (controller.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        final notifications = controller.notifications;

                        if (notifications.isEmpty) {
                          return const Center(
                            child: Text(
                              'Chưa có thông báo nào',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          );
                        }

                        return ListView.builder(
                          itemCount: notifications.length,
                          itemBuilder: (context, index) {
                            final notification = notifications[index];
                            return _buildNotificationItem(
                                notification, controller);
                          },
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createNotification(
      RealtimeNotificationController controller) async {
    if (_titleController.text.trim().isEmpty ||
        _bodyController.text.trim().isEmpty) {
      Get.snackbar(
        'Lỗi',
        'Vui lòng nhập tiêu đề và nội dung',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final notificationId = await controller.createNotification(
      title: _titleController.text.trim(),
      body: _bodyController.text.trim(),
      imageUrl: _imageUrlController.text.trim().isEmpty
          ? null
          : _imageUrlController.text.trim(),
      targetScreen: _targetScreenController.text.trim().isEmpty
          ? null
          : _targetScreenController.text.trim(),
      type: _selectedType,
      priority: _selectedPriority,
      isPublic: _isPublic,
      expiresIn: Duration(days: _expiresInDays),
    );

    if (notificationId != null) {
      // Xóa form
      _titleController.clear();
      _bodyController.clear();
      _imageUrlController.clear();
      _targetScreenController.clear();
      setState(() {
        _selectedType = 'system';
        _selectedPriority = 'normal';
        _isPublic = true;
        _expiresInDays = 30;
      });

      Get.snackbar(
        'Thành công',
        'Đã tạo thông báo mới',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'Lỗi',
        'Không thể tạo thông báo',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Widget _buildNotificationItem(
    NotificationViewModel notification,
    RealtimeNotificationController controller,
  ) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    // Xác định icon và màu sắc dựa trên loại thông báo
    IconData iconData = Icons.notifications;
    Color iconColor = Colors.blue;

    switch (notification.notification.type) {
      case 'movie':
        iconData = Icons.movie;
        iconColor = Colors.purple;
        break;
      case 'ticket':
        iconData = Icons.confirmation_number;
        iconColor = Colors.green;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = Colors.orange;
        break;
      case 'bug_report':
        iconData = Icons.bug_report;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.info;
        iconColor = Colors.blue;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    iconData,
                    color: iconColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notification.notification.title,
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        dateFormat.format(
                            notification.notification.createdAtDateTime),
                        style: GoogleFonts.mulish(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // Badges
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (notification.notification.priority != 'normal')
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getPriorityColor(
                              notification.notification.priority),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getPriorityLabel(notification.notification.priority),
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: notification.notification.isPublic
                            ? Colors.green
                            : Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        notification.notification.isPublic
                            ? 'CÔNG KHAI'
                            : 'RIÊNG TƯ',
                        style: GoogleFonts.mulish(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Nội dung
            Text(
              notification.notification.body,
              style: GoogleFonts.mulish(
                fontSize: 14,
                color: Colors.grey[700],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // Thông tin bổ sung
            Row(
              children: [
                _buildInfoChip(
                    'Loại', _getTypeLabel(notification.notification.type)),
                const SizedBox(width: 8),
                if (notification.notification.targetScreen != null)
                  _buildInfoChip(
                      'Đích', notification.notification.targetScreen!),
                const Spacer(),
                if (notification.notification.expiresAtDateTime
                    .isBefore(DateTime.now()))
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'HẾT HẠN',
                      style: GoogleFonts.mulish(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $value',
        style: GoogleFonts.mulish(
          fontSize: 11,
          color: Colors.grey[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'urgent':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  String _getPriorityLabel(String priority) {
    switch (priority) {
      case 'urgent':
        return 'KHẨN';
      case 'high':
        return 'CAO';
      case 'low':
        return 'THẤP';
      default:
        return 'BÌNH THƯỜNG';
    }
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'movie':
        return 'Phim';
      case 'ticket':
        return 'Vé';
      case 'promo':
        return 'Khuyến mãi';
      case 'bug_report':
        return 'Báo lỗi';
      default:
        return 'Hệ thống';
    }
  }
}

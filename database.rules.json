{"rules": {".read": "auth != null", ".write": "false", "notifications": {".read": "auth != null", ".write": "auth != null", "$notification_id": {".read": "auth != null", ".write": "auth != null"}}, "user_notifications": {".read": "auth != null", ".write": "auth != null", "$notificationKey": {".read": "auth != null && (data.child('userId').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin' || root.child('users').child(auth.uid).child('role').val() == 'developer')", ".write": "auth != null && (data.child('userId').val() == auth.uid || newData.child('userId').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin' || root.child('users').child(auth.uid).child('role').val() == 'developer')"}}, "bug_reports": {".read": "auth != null", ".write": "auth != null", "$bugReportId": {".read": "auth != null", ".write": "auth != null"}}, "chat_support": {"$chatId": {".read": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", "messages": {"$messageId": {".write": "auth != null && (data.parent().parent().child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')"}}}}, "seat_reservations": {".read": "auth != null", ".write": "auth != null"}, "user_roles": {"$userId": {".read": "auth != null && (auth.uid == $userId || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'"}}, "fcm_tokens": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId"}}, "analytics": {".read": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'", ".write": "auth != null"}}}
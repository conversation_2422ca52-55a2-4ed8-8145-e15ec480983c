import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_notification_model.dart';
import '../services/realtime_database_service.dart';
import 'auth_controller.dart';

class RealtimeNotificationController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final AuthController _authController = Get.find<AuthController>();

  // Observables
  final RxList<NotificationViewModel> _notifications =
      <NotificationViewModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _unreadCount = 0.obs;
  final RxInt _unseenCount = 0.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _selectedFilter = 'all'.obs;

  // Streams
  StreamSubscription? _publicNotificationsSubscription;
  StreamSubscription? _userNotificationsSubscription;

  // Getters
  List<NotificationViewModel> get notifications => _notifications;
  List<NotificationViewModel> get filteredNotifications {
    switch (_selectedFilter.value) {
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'read':
        return _notifications.where((n) => n.isRead).toList();
      case 'system':
        return _notifications
            .where((n) => n.notification.type == 'system')
            .toList();
      case 'movie':
        return _notifications
            .where((n) => n.notification.type == 'movie')
            .toList();
      case 'promo':
        return _notifications
            .where((n) => n.notification.type == 'promo')
            .toList();
      case 'ticket':
        return _notifications
            .where((n) => n.notification.type == 'ticket')
            .toList();
      default:
        return _notifications;
    }
  }

  bool get isLoading => _isLoading.value;
  int get unreadCount => _unreadCount.value;
  int get unseenCount => _unseenCount.value;
  String get errorMessage => _errorMessage.value;
  String get selectedFilter => _selectedFilter.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    super.onClose();
  }

  // Thiết lập lắng nghe sự thay đổi đăng nhập
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        _fetchNotifications();
      } else {
        _notifications.clear();
        _unreadCount.value = 0;
      }
    });

    // Fetch ngay lập tức nếu đã đăng nhập
    if (_authController.user != null) {
      _fetchNotifications();
    }
  }

  // Hủy các subscription
  void _cancelSubscriptions() {
    _publicNotificationsSubscription?.cancel();
    _userNotificationsSubscription?.cancel();
    _publicNotificationsSubscription = null;
    _userNotificationsSubscription = null;
  }

  // Lấy thông báo
  void _fetchNotifications() {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Lấy thông báo công khai
      _publicNotificationsSubscription = _realtimeService
          .getPublicNotificationsStream()
          .listen(_handlePublicNotifications, onError: _handleError);

      // Lấy thông báo của người dùng
      if (_authController.user?.id != null) {
        _userNotificationsSubscription = _realtimeService
            .getUserNotificationsStream(_authController.user!.id!)
            .listen(_handleUserNotifications, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải thông báo: $e';
      _isLoading.value = false;
    }
  }

  // Xử lý thông báo công khai
  void _handlePublicNotifications(
      List<RealtimeNotificationModel> publicNotifications) {
    print('Received ${publicNotifications.length} public notifications');

    // Lọc thông báo admin nếu không phải admin
    final filteredPublicNotifications =
        publicNotifications.where((notification) {
      final isAdminNotification =
          notification.data?['isAdminNotification'] == 'true';
      return !isAdminNotification || (_authController.user?.isAdmin ?? false);
    }).toList();

    // Chuyển đổi thành NotificationViewModel
    final viewModels = filteredPublicNotifications
        .map(
            (notification) => NotificationViewModel(notification: notification))
        .toList();

    // Cập nhật danh sách thông báo
    _updateNotifications(viewModels, isPublic: true);
  }

  // Xử lý thông báo của người dùng
  void _handleUserNotifications(
      List<RealtimeNotificationModel> userNotifications) {
    print('Received ${userNotifications.length} user notifications');

    // Chuyển đổi thành NotificationViewModel
    final viewModels = userNotifications
        .map(
            (notification) => NotificationViewModel(notification: notification))
        .toList();

    _updateNotifications(viewModels, isPublic: false);
  }

  // Cập nhật danh sách thông báo
  void _updateNotifications(List<NotificationViewModel> newNotifications,
      {required bool isPublic}) {
    // Nếu là thông báo công khai, xóa các thông báo công khai cũ
    if (isPublic) {
      _notifications.removeWhere((notification) =>
          notification.notification.isPublic &&
          notification.notification.targetUserIds == null);
    } else {
      // Nếu là thông báo cá nhân, xóa các thông báo cá nhân cũ
      _notifications.removeWhere((notification) =>
          !notification.notification.isPublic ||
          notification.notification.targetUserIds != null);
    }

    // Thêm thông báo mới
    _notifications.addAll(newNotifications);

    // Sắp xếp theo thời gian
    _notifications.sort(
        (a, b) => b.notification.createdAt.compareTo(a.notification.createdAt));

    // Cập nhật số lượng thông báo chưa đọc và chưa xem
    _updateCounts();

    _isLoading.value = false;
  }

  // Cập nhật số lượng thông báo chưa đọc và chưa xem
  void _updateCounts() {
    _unreadCount.value = _notifications.where((n) => !n.isRead).length;
    _unseenCount.value = _notifications.where((n) => !n.isSeen).length;
  }

  // Xử lý lỗi
  void _handleError(dynamic error) {
    print('Error in notification stream: $error');
    _errorMessage.value = 'Lỗi khi tải thông báo: $error';
    _isLoading.value = false;
  }

  // Đánh dấu thông báo đã đọc
  Future<bool> markAsRead(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsRead(
          _authController.user!.id!, notificationId);

      if (success) {
        // Cập nhật local state
        final index = _notifications
            .indexWhere((n) => n.notification.id == notificationId);
        if (index != -1) {
          final notification = _notifications[index];
          final updatedUserNotification =
              notification.userNotification?.copyWith(
                    isRead: true,
                    readAt: DateTime.now().millisecondsSinceEpoch,
                  ) ??
                  RealtimeUserNotificationModel(
                    id: '',
                    userId: _authController.user!.id!,
                    notificationId: notificationId,
                    isRead: true,
                    isSeen: true,
                    isDeleted: false,
                    createdAt: DateTime.now().millisecondsSinceEpoch,
                    readAt: DateTime.now().millisecondsSinceEpoch,
                    seenAt: DateTime.now().millisecondsSinceEpoch,
                  );

          _notifications[index] = NotificationViewModel(
            notification: notification.notification,
            userNotification: updatedUserNotification,
          );

          _updateCounts();
        }
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu thông báo đã xem
  Future<bool> markAsSeen(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.markNotificationAsSeen(
          _authController.user!.id!, notificationId);

      if (success) {
        // Cập nhật local state
        final index = _notifications
            .indexWhere((n) => n.notification.id == notificationId);
        if (index != -1) {
          final notification = _notifications[index];
          final updatedUserNotification =
              notification.userNotification?.copyWith(
                    isSeen: true,
                    seenAt: DateTime.now().millisecondsSinceEpoch,
                  ) ??
                  RealtimeUserNotificationModel(
                    id: '',
                    userId: _authController.user!.id!,
                    notificationId: notificationId,
                    isRead: false,
                    isSeen: true,
                    isDeleted: false,
                    createdAt: DateTime.now().millisecondsSinceEpoch,
                    seenAt: DateTime.now().millisecondsSinceEpoch,
                  );

          _notifications[index] = NotificationViewModel(
            notification: notification.notification,
            userNotification: updatedUserNotification,
          );

          _updateCounts();
        }
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã xem: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã đọc
  Future<bool> markAllAsRead() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsRead(_authController.user!.id!);

      if (success) {
        // Cập nhật local state
        for (int i = 0; i < _notifications.length; i++) {
          final notification = _notifications[i];
          if (!notification.isRead) {
            final updatedUserNotification =
                notification.userNotification?.copyWith(
                      isRead: true,
                      readAt: DateTime.now().millisecondsSinceEpoch,
                    ) ??
                    RealtimeUserNotificationModel(
                      id: '',
                      userId: _authController.user!.id!,
                      notificationId: notification.notification.id,
                      isRead: true,
                      isSeen: true,
                      isDeleted: false,
                      createdAt: DateTime.now().millisecondsSinceEpoch,
                      readAt: DateTime.now().millisecondsSinceEpoch,
                      seenAt: DateTime.now().millisecondsSinceEpoch,
                    );

            _notifications[i] = NotificationViewModel(
              notification: notification.notification,
              userNotification: updatedUserNotification,
            );
          }
        }

        _updateCounts();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã xem
  Future<bool> markAllAsSeen() async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService
          .markAllNotificationsAsSeen(_authController.user!.id!);

      if (success) {
        // Cập nhật local state
        for (int i = 0; i < _notifications.length; i++) {
          final notification = _notifications[i];
          if (!notification.isSeen) {
            final updatedUserNotification =
                notification.userNotification?.copyWith(
                      isSeen: true,
                      seenAt: DateTime.now().millisecondsSinceEpoch,
                    ) ??
                    RealtimeUserNotificationModel(
                      id: '',
                      userId: _authController.user!.id!,
                      notificationId: notification.notification.id,
                      isRead: false,
                      isSeen: true,
                      isDeleted: false,
                      createdAt: DateTime.now().millisecondsSinceEpoch,
                      seenAt: DateTime.now().millisecondsSinceEpoch,
                    );

            _notifications[i] = NotificationViewModel(
              notification: notification.notification,
              userNotification: updatedUserNotification,
            );
          }
        }

        _updateCounts();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã xem: $e';
      return false;
    }
  }

  // Xóa thông báo
  Future<bool> deleteNotification(String notificationId) async {
    if (_authController.user?.id == null) return false;

    try {
      final success = await _realtimeService.deleteNotification(
          _authController.user!.id!, notificationId);

      if (success) {
        // Cập nhật local state
        _notifications.removeWhere(
            (notification) => notification.notification.id == notificationId);
        _updateCounts();
      }

      return success;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa thông báo: $e';
      return false;
    }
  }

  // Làm mới thông báo
  void refreshNotifications() {
    _cancelSubscriptions();
    _notifications.clear();
    _fetchNotifications();
  }

  // Thay đổi bộ lọc
  void setFilter(String filter) {
    _selectedFilter.value = filter;
  }

  // Tạo thông báo mới (chỉ admin/developer)
  Future<String?> createNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = true,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
  }) async {
    try {
      return await _realtimeService.createNotification(
        title: title,
        body: body,
        imageUrl: imageUrl,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
        expiresIn: expiresIn,
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo thông báo: $e';
      return null;
    }
  }

  // Lấy thông báo theo loại
  List<NotificationViewModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.notification.type == type).toList();
  }

  // Lấy thông báo chưa đọc
  List<NotificationViewModel> get unreadNotifications {
    return _notifications.where((n) => !n.isRead).toList();
  }

  // Lấy thông báo đã đọc
  List<NotificationViewModel> get readNotifications {
    return _notifications.where((n) => n.isRead).toList();
  }

  // Lấy thông báo theo độ ưu tiên
  List<NotificationViewModel> getNotificationsByPriority(String priority) {
    return _notifications
        .where((n) => n.notification.priority == priority)
        .toList();
  }

  // Kiểm tra có thông báo mới không
  bool get hasNewNotifications => _unseenCount.value > 0;

  // Xóa lỗi
  void clearError() {
    _errorMessage.value = '';
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/realtime_notification_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/sample_notification_data.dart';

class NotificationSampleDataPage extends StatefulWidget {
  const NotificationSampleDataPage({Key? key}) : super(key: key);

  @override
  State<NotificationSampleDataPage> createState() =>
      _NotificationSampleDataPageState();
}

class _NotificationSampleDataPageState
    extends State<NotificationSampleDataPage> {
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<RealtimeNotificationController>();
    final authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Dữ liệu mẫu thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thông tin
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tạo dữ liệu mẫu',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Tạo các thông báo mẫu để test hệ thống thông báo realtime. Bao gồm các loại thông báo khác nhau với độ ưu tiên và thời gian hết hạn khác nhau.',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Nút tạo dữ liệu mẫu
              _buildSampleDataSection(),

              const SizedBox(height: 20),

              // Thống kê thông báo hiện tại
              _buildStatisticsSection(controller),

              const SizedBox(height: 20),

              // Danh sách thông báo gần đây
              _buildRecentNotificationsSection(controller),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSampleDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tạo dữ liệu mẫu',
              style: GoogleFonts.mulish(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Nút tạo thông báo công khai
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _isCreating ? null : () => _createSampleData('public'),
                icon: _isCreating
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.notifications),
                label: Text(
                  'Tạo thông báo công khai',
                  style: GoogleFonts.mulish(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff4B79A1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Nút tạo thông báo admin
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _isCreating ? null : () => _createSampleData('admin'),
                icon: _isCreating
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.admin_panel_settings),
                label: Text(
                  'Tạo thông báo admin',
                  style: GoogleFonts.mulish(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Nút tạo thông báo developer
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _isCreating ? null : () => _createSampleData('developer'),
                icon: _isCreating
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.code),
                label: Text(
                  'Tạo thông báo developer',
                  style: GoogleFonts.mulish(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Nút tạo tất cả
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isCreating ? null : () => _createSampleData('all'),
                icon: _isCreating
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.all_inclusive),
                label: Text(
                  'Tạo tất cả dữ liệu mẫu',
                  style: GoogleFonts.mulish(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(RealtimeNotificationController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thống kê thông báo',
              style: GoogleFonts.mulish(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final notifications = controller.notifications;
              final unreadCount = controller.unreadCount;
              final systemCount = notifications
                  .where((n) => n.notification.type == 'system')
                  .length;
              final movieCount = notifications
                  .where((n) => n.notification.type == 'movie')
                  .length;
              final promoCount = notifications
                  .where((n) => n.notification.type == 'promo')
                  .length;
              final ticketCount = notifications
                  .where((n) => n.notification.type == 'ticket')
                  .length;

              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard('Tổng số',
                            notifications.length.toString(), Colors.blue),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                            'Chưa đọc', unreadCount.toString(), Colors.red),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                            'Hệ thống', systemCount.toString(), Colors.grey),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                            'Phim', movieCount.toString(), Colors.purple),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                            'Khuyến mãi', promoCount.toString(), Colors.orange),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                            'Vé', ticketCount.toString(), Colors.green),
                      ),
                    ],
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.mulish(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentNotificationsSection(
      RealtimeNotificationController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Thông báo gần đây',
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    controller.refreshNotifications();
                  },
                  child: Text(
                    'Làm mới',
                    style: GoogleFonts.mulish(
                      color: const Color(0xff4B79A1),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Obx(() {
              final notifications = controller.notifications.take(5).toList();

              if (notifications.isEmpty) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text(
                      'Chưa có thông báo nào',
                      style: GoogleFonts.mulish(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                );
              }

              return ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: notifications.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      backgroundColor:
                          _getTypeColor(notification.notification.type)
                              .withOpacity(0.1),
                      child: Icon(
                        _getTypeIcon(notification.notification.type),
                        color: _getTypeColor(notification.notification.type),
                        size: 20,
                      ),
                    ),
                    title: Text(
                      notification.notification.title,
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(
                      notification.notification.body,
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color:
                                  _getTypeColor(notification.notification.type),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        const SizedBox(height: 4),
                        Text(
                          _formatTime(
                              notification.notification.createdAtDateTime),
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  Future<void> _createSampleData(String type) async {
    setState(() {
      _isCreating = true;
    });

    try {
      switch (type) {
        case 'public':
          await SampleNotificationData.createSampleNotifications();
          break;
        case 'admin':
          await SampleNotificationData.createAdminNotifications();
          break;
        case 'developer':
          await SampleNotificationData.createDeveloperNotifications();
          break;
        case 'all':
          await SampleNotificationData.createAllSampleData();
          break;
      }

      // Làm mới danh sách thông báo
      final controller = Get.find<RealtimeNotificationController>();
      controller.refreshNotifications();

      Get.snackbar(
        'Thành công',
        'Đã tạo dữ liệu mẫu thành công!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tạo dữ liệu mẫu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'movie':
        return Colors.purple;
      case 'ticket':
        return Colors.green;
      case 'promo':
        return Colors.orange;
      case 'bug_report':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'movie':
        return Icons.movie;
      case 'ticket':
        return Icons.confirmation_number;
      case 'promo':
        return Icons.local_offer;
      case 'bug_report':
        return Icons.bug_report;
      default:
        return Icons.info;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Vừa xong';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}p';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
